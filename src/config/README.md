# Config Directory

This directory contains application configuration files and settings management for different environments and services.

## Structure

```
config/
├── index.ts               # Main configuration exports and utilities
├── config.ts              # Core application configuration
└── mongodb.ts             # MongoDB database configuration
```

## Configuration Files

### Core Configuration (`config.ts`)
- Application-wide settings and constants
- Environment-specific configuration values
- Feature flags and toggles
- API endpoints and service URLs
- Security settings and secrets management

### Database Configuration (`mongodb.ts`)
- MongoDB connection configuration
- Database connection pooling settings
- Connection health checks and monitoring
- Database migration and setup utilities
- Connection string management for different environments

### Configuration Index (`index.ts`)
- Centralized configuration exports
- Configuration validation and type checking
- Environment variable parsing and validation
- Default value management
- Configuration utility functions

## Key Features

### Environment Management
- Development, staging, and production configurations
- Environment variable validation and parsing
- Secure handling of sensitive configuration data
- Configuration override mechanisms

### Database Configuration
- MongoDB connection string management
- Connection pooling and optimization settings
- Database health monitoring and diagnostics
- Automatic reconnection and failover handling

### Security Configuration
- JWT secret management
- API key and token configuration
- CORS and security header settings
- Rate limiting configuration

### Service Integration
- External API configuration (OpenAI, authentication providers)
- Service endpoint management
- Timeout and retry configuration
- Service health check settings

## Design Principles

### Configuration Management
- **Type Safety**: All configuration values are typed
- **Validation**: Runtime validation of configuration values
- **Environment Separation**: Clear separation between environments
- **Security**: Secure handling of sensitive data
- **Flexibility**: Easy configuration updates and overrides

### Best Practices
- Use environment variables for sensitive data
- Provide sensible defaults for optional configuration
- Validate configuration at application startup
- Document all configuration options
- Use TypeScript for configuration type safety

## Usage Patterns

### Configuration Access
```typescript
import { config } from '@/config';

// Access database configuration
const dbUrl = config.database.url;

// Access API configuration
const apiKey = config.openai.apiKey;
```

### Environment-Specific Settings
- Development: Local database, debug logging, relaxed security
- Staging: Production-like setup with test data
- Production: Optimized performance, strict security, monitoring

## Development Guidelines

- Always validate configuration values at startup
- Use TypeScript interfaces for configuration structure
- Provide clear error messages for missing configuration
- Document environment variables and their purposes
- Use configuration factories for complex setup logic
- Implement configuration hot-reloading for development
