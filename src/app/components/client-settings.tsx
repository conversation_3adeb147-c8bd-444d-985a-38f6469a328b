'use client';

import { Button, Translate, useTheme } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useAuth } from '@/contexts/auth-context';
import { useSimpleFloatingUI } from '@/contexts/simple-floating-context';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { Language } from '@prisma/client';
import { Languages, LogOut, Monitor, Moon, Settings, Sun, User, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';

export function ClientSettings() {
	const { theme, setTheme } = useTheme();
	const { language, setLanguage, t } = useTranslation();
	const { logout, user } = useAuth();
	const router = useRouter();
	const [isOpen, setIsOpen] = useState(false);

	const handleLogout = async () => {
		try {
			await logout();
			router.push('/login');
		} catch (error) {
			console.error('Logout failed:', error);
		}
	};

	// Settings content component - memoized to prevent re-renders
	const settingsContent = useMemo(
		() => (
			<div className="floating-settings-panel">
				{!isOpen ? (
					// Settings button
					<Button
						size="icon"
						className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
						onClick={() => setIsOpen(true)}
					>
						<Settings className="h-6 w-6" />
					</Button>
				) : (
					// Settings panel
					<div className="bg-background border rounded-lg shadow-lg w-64 p-4">
						<div className="flex items-center justify-between mb-4">
							<h3 className="text-lg font-semibold">{t('settings.title')}</h3>
							<Button
								size="icon"
								variant="ghost"
								className="h-8 w-8"
								onClick={() => setIsOpen(false)}
							>
								<X className="h-4 w-4" />
							</Button>
						</div>

						{/* User Section */}
						{user && (
							<div className="mb-4">
								<div className="flex items-center gap-2 mb-2">
									<User className="h-4 w-4" />
									<span className="text-sm font-medium">{t('user.account')}</span>
								</div>
								<Button
									variant="destructive"
									size="sm"
									className="w-full"
									onClick={handleLogout}
								>
									<LogOut className="h-4 w-4 mr-2" />
									{t('auth.logout')}
								</Button>
							</div>
						)}

						{/* Language Section */}
						<div className="mb-4">
							<div className="flex items-center gap-2 mb-2">
								<Languages className="h-4 w-4" />
								<span className="text-sm font-medium">Language</span>
							</div>
							<div className="space-y-1">
								<Button
									variant={language === Language.EN ? 'default' : 'ghost'}
									size="sm"
									className="w-full justify-start"
									onClick={() => setLanguage(Language.EN)}
								>
									<Translate text={getTranslationKeyOfLanguage(Language.EN)} />
								</Button>
								<Button
									variant={language === Language.VI ? 'default' : 'ghost'}
									size="sm"
									className="w-full justify-start"
									onClick={() => setLanguage(Language.VI)}
								>
									<Translate text={getTranslationKeyOfLanguage(Language.VI)} />
								</Button>
							</div>
						</div>

						{/* Theme Section */}
						<div>
							<div className="flex items-center gap-2 mb-2">
								<Monitor className="h-4 w-4" />
								<span className="text-sm font-medium">Theme</span>
							</div>
							<div className="space-y-1">
								<Button
									variant={theme === 'light' ? 'default' : 'ghost'}
									size="sm"
									className="w-full justify-start"
									onClick={() => setTheme('light')}
								>
									<Sun className="h-4 w-4 mr-2" />
									{t('theme.light')}
								</Button>
								<Button
									variant={theme === 'dark' ? 'default' : 'ghost'}
									size="sm"
									className="w-full justify-start"
									onClick={() => setTheme('dark')}
								>
									<Moon className="h-4 w-4 mr-2" />
									{t('theme.dark')}
								</Button>
								<Button
									variant={theme === 'system' ? 'default' : 'ghost'}
									size="sm"
									className="w-full justify-start"
									onClick={() => setTheme('system')}
								>
									<Monitor className="h-4 w-4 mr-2" />
									{t('theme.system')}
								</Button>
							</div>
						</div>
					</div>
				)}
			</div>
		),
		[isOpen, t, user, handleLogout, language, setLanguage, theme, setTheme]
	);

	// Use simple floating UI system
	useSimpleFloatingUI('client-settings', settingsContent, {
		position: { bottom: 16, right: 16 },
		zIndex: 1200,
		autoShow: true,
	});

	return null; // Content is rendered through floating UI system
}
