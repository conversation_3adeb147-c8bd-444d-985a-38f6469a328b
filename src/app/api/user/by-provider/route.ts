import { ValidationError } from '@/backend/errors';
import { getUserService } from '@/backend/wire';
import { UserWithDetail } from '@/models/user';
import { Provider } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { provider, providerId } = body;

		if (!provider) {
			throw new ValidationError('Provider is required to fetch user by provider details.');
		}

		if (!providerId) {
			throw new ValidationError('Provider ID is required to fetch user by provider details.');
		}

		if (!Object.values(Provider).includes(provider)) {
			throw new ValidationError(`Invalid provider: ${provider}`);
		}

		const userService = getUserService();
		const user = await userService.getUserByProvider(provider, providerId);

		return NextResponse.json(user as UserWithDetail | null);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		console.error('Failed to get user by provider:', error);
		return NextResponse.json({ error: 'Failed to fetch user by provider. Please try again.' }, { status: 500 });
	}
}
