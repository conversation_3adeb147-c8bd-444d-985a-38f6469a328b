import { ValidationError } from '@/backend/errors';
import { getWordService } from '@/backend/wire';
import { WordDetail } from '@/models';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { wordIds } = body;

		if (!Array.isArray(wordIds) || wordIds.length === 0) {
			throw new ValidationError('Word IDs array is required and cannot be empty.');
		}

		if (wordIds.length > 50) {
			throw new ValidationError('Cannot fetch more than 50 words by IDs at a time.');
		}

		// Validate that all wordIds are strings
		if (!wordIds.every(id => typeof id === 'string' && id.trim() !== '')) {
			throw new ValidationError('All word IDs must be non-empty strings.');
		}

		const wordService = getWordService();
		const words = await wordService.getWordsByIds(wordIds);

		return NextResponse.json(words as WordDetail[]);
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		console.error('Failed to fetch words by IDs:', error);
		return NextResponse.json({ error: 'Failed to fetch words by IDs. Please try again.' }, { status: 500 });
	}
}
