import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
	env: {},
	reactStrictMode: false,
	images: {
		unoptimized: true,
	},
	serverExternalPackages: ['ioredis'],
	webpack: (config, { isServer }) => {
		// Exclude server-only packages from client bundle
		if (!isServer) {
			config.resolve.fallback = {
				...config.resolve.fallback,
				fs: false,
				net: false,
				dns: false,
				tls: false,
				crypto: false,
				ioredis: false,
			};
		}
		return config;
	},
};

export default nextConfig;
